// ENS utility functions
import { ethers } from 'ethers';
import { getN<PERSON><PERSON><PERSON><PERSON>Address, NAMEWRAPPER_CONTRACT_ABI } from './contracts/name-wrapper';

/**
 * Get provider from connected wallet or create a default one
 */
export function getWalletProvider(): ethers.BrowserProvider | null {
  if (typeof window !== 'undefined' && window.ethereum) {
    try {
      return new ethers.BrowserProvider(window.ethereum);
    } catch {
      return null;
    }
  }
  return null;
}

// ENS Registry Contract Address
export const ENS_REGISTRY_ADDRESS = '******************************************';

// ENS Registry ABI (minimal for ownership checks)
export const ENS_REGISTRY_ABI = [
  'function owner(bytes32 node) view returns (address)',
  'function setOwner(bytes32 node, address owner)',
  'function resolver(bytes32 node) view returns (address)'
];

// Public Resolver ABI (minimal for avatar/text records)
export const PUBLIC_RESOLVER_ABI = [
  'function text(bytes32 node, string calldata key) view returns (string memory)',
  'function addr(bytes32 node) view returns (address)'
];

/**
 * Generate namehash for ENS name
 */
export function namehash(name: string): string {
  if (!name) return '******************************************000000000000000000000000';
  
  const labels = name.split('.');
  let hash = '******************************************000000000000000000000000';
  
  for (let i = labels.length - 1; i >= 0; i--) {
    const labelHash = ethers.keccak256(ethers.toUtf8Bytes(labels[i]));
    hash = ethers.keccak256(ethers.concat([hash, labelHash]));
  }
  
  return hash;
}

/**
 * Validate ENS name format (supports all ENS domains)
 */
export function isValidENSName(name: string): boolean {
  if (!name) return false;

  // Support all ENS domains including .eth, .test, .xyz, .com, etc.
  // ENS supports many TLDs, so we'll be more permissive
  const ensRegex = /^[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/;
  const isValidFormat = ensRegex.test(name.toLowerCase());

  // Additional validation: no consecutive dots, no leading/trailing dots
  const hasConsecutiveDots = /\.\./.test(name);
  const hasLeadingTrailingDots = name.startsWith('.') || name.endsWith('.');

  return isValidFormat && !hasConsecutiveDots && !hasLeadingTrailingDots;
}

/**
 * Check if ENS name is a testnet domain
 */
export function isTestnetENS(name: string): boolean {
  return name.toLowerCase().endsWith('.test');
}

/**
 * Get appropriate provider based on ENS domain
 */
export function getProviderForENS(ensName: string, chainId?: number): ethers.JsonRpcProvider {
  // If chainId is provided, use it to determine the provider
  if (chainId) {
    switch (chainId) {
      case 1: // Mainnet
        return new ethers.JsonRpcProvider('https://eth.llamarpc.com');
      case 11155111: // Sepolia
        return new ethers.JsonRpcProvider('https://sepolia.infura.io/v3/YOUR_INFURA_KEY');
      case 5: // Goerli
        return new ethers.JsonRpcProvider('https://goerli.infura.io/v3/YOUR_INFURA_KEY');
      default:
        return new ethers.JsonRpcProvider('https://eth.llamarpc.com');
    }
  }

  // Fallback to domain-based detection
  if (isTestnetENS(ensName)) {
    return new ethers.JsonRpcProvider('https://sepolia.infura.io/v3/YOUR_INFURA_KEY');
  }

  return new ethers.JsonRpcProvider('https://eth.llamarpc.com');
}

/**
 * Get ENS owner using ethers provider (supports mainnet and testnet)
 */
export async function getENSOwner(ensName: string, customProvider?: ethers.Provider, chainId?: number): Promise<string | null> {
  try {
    if (!isValidENSName(ensName)) {
      throw new Error('Invalid ENS name format');
    }

    // Use provided provider or create appropriate one based on ENS domain/chain
    const provider = customProvider || getProviderForENS(ensName, chainId);

    const ensRegistry = new ethers.Contract(ENS_REGISTRY_ADDRESS, ENS_REGISTRY_ABI, provider);
    const node = namehash(ensName.toLowerCase());
    const owner = await ensRegistry.owner(node);

    return owner === '******************************************' ? null : owner;
  } catch {
    // Silently handle error
    return null;
  }
}

/**
 * Transfer ENS ownership
 */
// Note: ethereum property is already defined in global.d.ts

export async function transferENSOwnership(
  ensName: string, 
  newOwner: string,
  customSigner?: ethers.Signer
): Promise<ethers.TransactionResponse> {
  if (!isValidENSName(ensName)) {
    throw new Error('Invalid ENS name format');
  }

  if (!ethers.isAddress(newOwner)) {
    throw new Error('Invalid new owner address');
  }

  // If no custom signer is provided, get the signer from the window.ethereum object
  // This will work with RainbowKit as it injects into window.ethereum
  if (!customSigner) {
    if (typeof window !== 'undefined' && window.ethereum) {
      const provider = new ethers.BrowserProvider(window.ethereum);
      customSigner = await provider.getSigner();
    } else {
      throw new Error('No ethereum provider found. Please connect with RainbowKit first.');
    }
  }

  const ensRegistry = new ethers.Contract(ENS_REGISTRY_ADDRESS, ENS_REGISTRY_ABI, customSigner);
  const node = namehash(ensName.toLowerCase());
  
  return await ensRegistry.setOwner(node, newOwner);
}

/**
 * Get ENS avatar/profile image
 */
export async function getENSAvatar(ensName: string, customProvider?: ethers.Provider): Promise<string | null> {
  try {
    if (!isValidENSName(ensName)) {
      return null;
    }

    // Use provided provider or create a default one
    const provider = customProvider || new ethers.JsonRpcProvider('https://eth.llamarpc.com');
    
    const ensRegistry = new ethers.Contract(ENS_REGISTRY_ADDRESS, ENS_REGISTRY_ABI, provider);
    const node = namehash(ensName.toLowerCase());
    const resolverAddress = await ensRegistry.resolver(node);
    
    if (resolverAddress === '******************************************') {
      return null;
    }

    const resolver = new ethers.Contract(resolverAddress, PUBLIC_RESOLVER_ABI, provider);
    const avatar = await resolver.text(node, 'avatar');
    
    return avatar || null;
  } catch {
    // Silently handle error
    return null;
  }
}

/**
 * Shorten address for display
 */
export function shortenAddress(address: string): string {
  if (!address) return '';
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

/**
 * Execute a prepared transaction from the backend
 */
export async function executePreparedTransaction(
  transactionData: {
    to: string;
    data: string;
    value: string;
    gas: string;
    chainId: number;
  },
  customSigner?: ethers.Signer
): Promise<ethers.TransactionResponse> {
  // Validate transaction data first
  if (!ethers.isAddress(transactionData.to)) {
    throw new Error('Invalid contract address');
  }

  // Log transaction data for debugging
  console.log('Executing transaction:', transactionData);

  // If no custom signer is provided, get the signer from the window.ethereum object
  if (!customSigner) {
    if (typeof window !== 'undefined' && window.ethereum) {
      const provider = new ethers.BrowserProvider(window.ethereum);
      customSigner = await provider.getSigner();
    } else {
      throw new Error('No ethereum provider found. Please connect with RainbowKit first.');
    }
  }

  // Verify the signer address matches the expected from address if provided
  const signerAddress = await customSigner.getAddress();
  console.log('Signer address:', signerAddress);
  console.log('Transaction data:', transactionData);

  // Prepare transaction object
  const tx = {
    to: transactionData.to,
    data: transactionData.data,
    value: transactionData.value,
    gasLimit: transactionData.gas,
  };

  // Execute the transaction
  return await customSigner.sendTransaction(tx);
}

/**
 * Get the token ID for an ENS name (for NameWrapper transfers)
 */
export function getENSTokenId(ensName: string): string {
  if (!isValidENSName(ensName)) {
    throw new Error('Invalid ENS name format');
  }

  // For NameWrapper, the token ID is the namehash of the ENS name
  const hash = namehash(ensName.toLowerCase());
  // Convert hex to decimal string
  return BigInt(hash).toString();
}

// Note: Transaction preparation is now handled by the backend API
// These functions have been removed to avoid duplicate logic
// Use apiService.prepareRegistrarTransaction() and apiService.prepareNameWrapperTransfer() instead

/**
 * Validate chain name
 */
export function isValidChain(chain: string): boolean {
  const validChains = ['mainnet', 'sepolia', 'goerli'];
  return validChains.includes(chain.toLowerCase());
}

/**
 * Get chain ID from chain name
 */
export function getChainId(chain: string): number {
  const chainMap: Record<string, number> = {
    'mainnet': 1,
    'sepolia': 11155111,
    'goerli': 5,
    'holesky': 17000,
  };

  return chainMap[chain.toLowerCase()] || 1;
}

/**
 * Validate that a contract exists at the given address
 */
export async function validateContractExists(contractAddress: string, provider: ethers.Provider): Promise<boolean> {
  try {
    const code = await provider.getCode(contractAddress);
    return code !== '0x';
  } catch (error) {
    console.error('Error checking contract existence:', error);
    return false;
  }
}

/**
 * Enhanced ENS name validation with detailed error reporting
 */
export function validateENSNameDetailed(name: string): { isValid: boolean; error?: string } {
  if (!name) {
    return { isValid: false, error: 'ENS name is required' };
  }

  if (name.length < 3) {
    return { isValid: false, error: 'ENS name must be at least 3 characters long' };
  }

  if (name.length > 255) {
    return { isValid: false, error: 'ENS name is too long (max 255 characters)' };
  }

  // Check for valid format
  const ensRegex = /^[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/;
  if (!ensRegex.test(name.toLowerCase())) {
    return { isValid: false, error: 'Invalid ENS name format. Must be like "example.eth" or "mydomain.com"' };
  }

  // Check for consecutive dots
  if (/\.\./.test(name)) {
    return { isValid: false, error: 'ENS name cannot contain consecutive dots' };
  }

  // Check for leading/trailing dots
  if (name.startsWith('.') || name.endsWith('.')) {
    return { isValid: false, error: 'ENS name cannot start or end with a dot' };
  }

  // Check for invalid characters
  const invalidChars = /[^a-zA-Z0-9.-]/;
  if (invalidChars.test(name)) {
    return { isValid: false, error: 'ENS name contains invalid characters. Only letters, numbers, dots, and hyphens are allowed' };
  }

  return { isValid: true };
}

/**
 * Validate network configuration for ENS operations
 */
export function validateNetworkForENS(chain: string, chainId: number): { isValid: boolean; error?: string } {
  const expectedChainId = getChainId(chain);

  if (chainId !== expectedChainId) {
    return {
      isValid: false,
      error: `Network mismatch: Expected ${chain} (chain ID ${expectedChainId}) but connected to chain ID ${chainId}. Please switch networks in your wallet.`
    };
  }

  // Check if the chain is supported
  const supportedChains = ['mainnet', 'sepolia', 'goerli'];
  if (!supportedChains.includes(chain.toLowerCase())) {
    return {
      isValid: false,
      error: `Unsupported network: ${chain}. Supported networks are: ${supportedChains.join(', ')}`
    };
  }

  return { isValid: true };
}

/**
 * Extract the created contract address from a factory transaction receipt
 */
export function extractContractAddressFromReceipt(receipt: any): string | null {
  try {
    if (!receipt || !receipt.logs) {
      console.warn('No receipt or logs found');
      return null;
    }

    console.log('Analyzing transaction receipt for contract address...');
    console.log('Receipt logs:', receipt.logs);

    // Look for the SubnameContractCreated event
    // Event signature: SubnameContractCreated(address indexed owner, address contractAddress, bytes32 parentNode)
    const eventSignature = ethers.keccak256(ethers.toUtf8Bytes('SubnameContractCreated(address,address,bytes32)'));
    console.log('Looking for event signature:', eventSignature);

    for (const log of receipt.logs) {
      console.log('Checking log:', log);
      if (log.topics && log.topics[0] === eventSignature) {
        console.log('Found SubnameContractCreated event!');

        // The contractAddress is the second parameter (not indexed), so it's in the data field
        // Data format: [contractAddress (32 bytes), parentNode (32 bytes)]
        if (log.data && log.data.length >= 66) {
          // Extract the contract address from the data field (first 32 bytes)
          // Remove 0x prefix, take first 64 chars (32 bytes), then get last 40 chars (20 bytes = address)
          const dataWithoutPrefix = log.data.slice(2);
          const contractAddressHex = dataWithoutPrefix.slice(24, 64); // Skip padding, take address
          const contractAddress = '0x' + contractAddressHex;
          console.log('Extracted contract address from event:', contractAddress);
          return contractAddress;
        }
      }
    }

    console.warn('SubnameContractCreated event not found in transaction logs');
    console.log('Available event signatures in logs:', receipt.logs.map((log: any) => log.topics?.[0]));
    return null;
  } catch (error) {
    console.error('Error extracting contract address from receipt:', error);
    return null;
  }
}

/**
 * Check if an address owns an ENS name
 */
export async function checkENSOwnership(ensName: string, ownerAddress: string, chain: string): Promise<{ isOwner: boolean; actualOwner?: string; error?: string }> {
  try {
    const rpcUrl = chain === 'sepolia'
      ? 'https://sepolia.drpc.org'
      : 'https://eth.llamarpc.com';
    const provider = new ethers.JsonRpcProvider(rpcUrl);

    const actualOwner = await getENSOwner(ensName, provider);

    if (!actualOwner) {
      return {
        isOwner: false,
        error: `ENS name "${ensName}" is not registered or has no owner`
      };
    }

    const isOwner = actualOwner.toLowerCase() === ownerAddress.toLowerCase();

    return {
      isOwner,
      actualOwner,
      error: isOwner ? undefined : `You don't own "${ensName}". Current owner: ${actualOwner}`
    };
  } catch (error) {
    return {
      isOwner: false,
      error: `Failed to check ENS ownership: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Check if an ENS name is wrapped in the NameWrapper contract
 */
export async function isENSNameWrapped(
  ensName: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<boolean> {
  try {
    if (!isValidENSName(ensName)) {
      return false;
    }

    // Get the appropriate provider
    const provider = customProvider || getProviderForENS(ensName, getChainId(chain));

    // Get NameWrapper contract for the specified chain
    const nameWrapperAddress = getNameWrapperAddress(chain);
    const nameWrapperContract = new ethers.Contract(
      nameWrapperAddress,
      NAMEWRAPPER_CONTRACT_ABI,
      provider
    );

    // Get the token ID for the ENS name
    const tokenId = getENSTokenId(ensName);

    // Check if the NameWrapper contract has a balance for this token ID
    // If balance > 0, the name is wrapped
    const balance = await nameWrapperContract.balanceOf(nameWrapperAddress, BigInt(tokenId));
    return balance > 0n;

  } catch (error) {
    console.error('Error checking if ENS name is wrapped:', error);
    return false;
  }
}

/**
 * Get the owner of a wrapped ENS name from the NameWrapper contract
 */
export async function getWrappedENSOwner(
  ensName: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<string | null> {
  try {
    if (!isValidENSName(ensName)) {
      return null;
    }

    // Get the appropriate provider
    const provider = customProvider || getProviderForENS(ensName, getChainId(chain));

    // Get NameWrapper contract for the specified chain
    const nameWrapperAddress = getNameWrapperAddress(chain);
    const nameWrapperContract = new ethers.Contract(
      nameWrapperAddress,
      NAMEWRAPPER_CONTRACT_ABI,
      provider
    );

    // Get the token ID for the ENS name
    const tokenId = getENSTokenId(ensName);

    // Get the owner of the wrapped name
    const owner = await nameWrapperContract.ownerOf(BigInt(tokenId));
    return owner === '******************************************' ? null : owner;

  } catch (error) {
    console.error('Error getting wrapped ENS owner:', error);
    return null;
  }
}

// Note: NameWrapper transfer preparation is now handled by the backend API
// Use apiService.prepareNameWrapperTransfer() instead

/**
 * Validate that an ENS name can be transferred via NameWrapper
 */
export async function validateNameWrapperTransfer(
  ensName: string,
  fromAddress: string,
  toAddress: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<{ isValid: boolean; error?: string }> {
  try {
    // Basic validation
    if (!isValidENSName(ensName)) {
      return { isValid: false, error: 'Invalid ENS name format' };
    }

    if (!ethers.isAddress(fromAddress)) {
      return { isValid: false, error: 'Invalid from address' };
    }

    if (!ethers.isAddress(toAddress)) {
      return { isValid: false, error: 'Invalid to address' };
    }

    if (fromAddress.toLowerCase() === toAddress.toLowerCase()) {
      return { isValid: false, error: 'From and to addresses cannot be the same' };
    }

    // Check if the name is wrapped
    const isWrapped = await isENSNameWrapped(ensName, chain, customProvider);
    if (!isWrapped) {
      return {
        isValid: false,
        error: `${ensName} is not wrapped in the NameWrapper contract. Only wrapped names can be transferred via NameWrapper.`
      };
    }

    // Check if the from address is the actual owner
    const actualOwner = await getWrappedENSOwner(ensName, chain, customProvider);
    if (!actualOwner || actualOwner.toLowerCase() !== fromAddress.toLowerCase()) {
      return {
        isValid: false,
        error: `${fromAddress} is not the owner of ${ensName}. Current owner: ${actualOwner || 'Unknown'}`
      };
    }

    return { isValid: true };

  } catch (error) {
    console.error('Error validating NameWrapper transfer:', error);
    return {
      isValid: false,
      error: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}