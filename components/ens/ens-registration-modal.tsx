'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useChainId } from 'wagmi';
import { Button } from '@/components/ui/button';
import { ENSDebugInfo } from './ens-debug-info';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/lib/toast-context';
import { useAuth } from '@/lib/auth-context';
import { apiService } from '@/lib/api';
import { ApplicationWithApiKey } from '@/lib/api';
import {
  CheckCircleIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  WalletIcon,
  GlobeIcon,
  ArrowUpDownIcon,
  LoaderIcon,
  XIcon,
  SettingsIcon
} from 'lucide-react';

import { ConnectButton } from '@rainbow-me/rainbowkit';
import { ApplicationSelection } from '@/components/ens/application-selection';

interface ENSRegistrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: any) => void;
  className?: string;
}

type ModalStep = 'connect' | 'select-app' | 'enter-ens' | 'transfer-ens' | 'store-registration';

interface ENSModalState {
  currentStep: ModalStep;
  ensName: string;
  selectedApplication: ApplicationWithApiKey | null;
  createdContractAddress?: string;
  chain: string;
  registrarTxHash?: string;
  transferTxHash?: string;
  isLoading: boolean;
  error?: string;
  registrarCompleted: boolean;
  transferCompleted: boolean;
  storeCompleted: boolean;
}

export function ENSRegistrationModal({ isOpen, onClose, onSuccess }: ENSRegistrationModalProps) {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { showToast } = useToast();
  const { token } = useAuth();

  const [state, setState] = useState<ENSModalState>({
    currentStep: 'connect',
    ensName: '',
    selectedApplication: null,
    chain: 'sepolia',
    isLoading: false,
    registrarCompleted: false,
    transferCompleted: false,
    storeCompleted: false
  });

  const [showDebugInfo, setShowDebugInfo] = useState(false);

  // Contract write hooks for registrar creation
  const {
    writeContract: writeFactoryContract,
    data: registrarTxHash,
    error: registrarTxError,
    isPending: isRegistrarPending
  } = useWriteContract();

  // Contract write hooks for ENS transfer
  const {
    writeContract: writeNameWrapperContract,
    data: transferTxHash,
    error: transferTxError,
    isPending: isTransferPending
  } = useWriteContract();

  // Wait for registrar transaction confirmation with timeout
  const {
    data: registrarReceipt,
    isLoading: isRegistrarConfirming,
    isSuccess: isRegistrarConfirmed,
    error: registrarConfirmError
  } = useWaitForTransactionReceipt({
    hash: registrarTxHash,
    timeout: 120000, // 2 minutes timeout
  });

  // Wait for transfer transaction confirmation with timeout
  const {
    isLoading: isTransferConfirming,
    isSuccess: isTransferConfirmed,
    error: transferConfirmError
  } = useWaitForTransactionReceipt({
    hash: transferTxHash,
    timeout: 120000, // 2 minutes timeout
  });

  // Store ENS registration function
  const handleStoreENS = useCallback(async () => {
    if (!token || !state.ensName || !state.createdContractAddress || !state.selectedApplication) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      console.log('=== FRONTEND: Storing ENS registration ===');
      const storeParams = {
        ens_name: state.ensName,
        contractAddress: state.createdContractAddress,
        chain: state.chain,
        isActive: true
      };
      console.log('Store parameters:', JSON.stringify(storeParams, null, 2));

      // Step 3: Store ENS registration in backend using the created contract address
      const response = await apiService.registerENSRoot(
        storeParams,
        token,
        state.selectedApplication.appId
      );

      console.log('=== FRONTEND: Store ENS API response ===');
      console.log('Full response:', JSON.stringify(response, null, 2));

      if (!response.success) {
        console.error('Store ENS API call failed:', response.error);
        throw new Error(response.error || 'Failed to store ENS registration');
      }

      console.log('=== FRONTEND: ENS registration stored successfully ===');

      setState(prev => ({
        ...prev,
        isLoading: false,
        storeCompleted: true
      }));

      showToast({
        type: 'success',
        title: 'ENS Registration Complete! 🎉',
        description: `${state.ensName} has been successfully registered and linked to ${state.selectedApplication.name}`
      });

      // Close modal and refresh dashboard
      setTimeout(() => {
        onSuccess?.(response.data);
        onClose();
      }, 2000);

    } catch (error) {
      console.error('=== FRONTEND: Store ENS failed ===', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to store ENS registration'
      }));
      showToast({
        type: 'error',
        title: 'Storage Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  }, [token, state.ensName, state.createdContractAddress, state.chain, state.selectedApplication, showToast, onSuccess, onClose]);



  // Handle registrar transaction confirmation - extract contract address from receipt
  useEffect(() => {
    if (isRegistrarConfirmed && registrarTxHash && registrarReceipt && state.selectedApplication) {
      console.log('=== FRONTEND: Registrar transaction confirmed, extracting contract address ===', registrarTxHash);

      const processRegistrarTransaction = async () => {
        try {
          console.log('Registrar transaction confirmed:', registrarTxHash);
          console.log('Transaction receipt:', registrarReceipt);

          // Extract the actual contract address from the transaction receipt
          const { extractContractAddressFromReceipt } = await import('@/lib/ens-utils');
          let createdContractAddress = extractContractAddressFromReceipt(registrarReceipt);

          // Fallback: If we can't extract from events, try to get it from the factory contract
          if (!createdContractAddress) {
            console.warn('Could not extract contract address from events, trying fallback method...');

            try {
              // Query the factory contract to get the latest contract created by this user
              const { ethers } = await import('ethers');
              const { FACTORY_CONTRACT_ABI } = await import('@/lib/contracts/factory-contract');
              const rpcUrl = state.chain === 'sepolia'
                ? 'https://sepolia.drpc.org'
                : 'https://eth.llamarpc.com';
              const provider = new ethers.JsonRpcProvider(rpcUrl);
              const factoryContract = new ethers.Contract(FACTORY_CONTRACT_ADDRESS, FACTORY_CONTRACT_ABI, provider);

              // Get contracts owned by the user (this might return the latest one)
              const contracts = await factoryContract.getSubnameContractsByOwner(address);
              if (contracts && contracts.length > 0) {
                createdContractAddress = contracts[contracts.length - 1]; // Get the latest one
                console.log('Fallback: Found contract address via factory query:', createdContractAddress);
              }
            } catch (fallbackError) {
              console.error('Fallback method also failed:', fallbackError);
            }
          }

          if (!createdContractAddress) {
            throw new Error('Failed to extract contract address from transaction receipt. The transaction may have failed or the event was not emitted properly. Please check the transaction on Etherscan and try again.');
          }

          console.log('Successfully extracted contract address:', createdContractAddress);

          setState(prev => ({
            ...prev,
            registrarTxHash: registrarTxHash,
            currentStep: 'transfer-ens',
            isLoading: false,
            registrarCompleted: true,
            createdContractAddress: createdContractAddress
          }));

          showToast({
            type: 'success',
            title: 'Registrar Created Successfully! 🎉',
            description: `Subname registrar contract created at ${createdContractAddress.slice(0, 6)}...${createdContractAddress.slice(-4)}`
          });
        } catch (error) {
          console.error('Failed to process registrar transaction:', error);
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Transaction processing failed'
          }));
          showToast({
            type: 'error',
            title: 'Contract Address Extraction Failed',
            description: error instanceof Error ? error.message : 'Unknown error occurred'
          });
        }
      };

      processRegistrarTransaction();
    }
  }, [isRegistrarConfirmed, registrarTxHash, registrarReceipt, state.ensName, state.chain, state.selectedApplication, token, showToast, address]);

  // Handle transfer transaction confirmation - send signed data to backend
  useEffect(() => {
    if (isTransferConfirmed && transferTxHash && state.selectedApplication && state.createdContractAddress) {
      console.log('=== FRONTEND: Transfer transaction confirmed, sending to backend ===', transferTxHash);

      const sendSignedTransferToBackend = async () => {
        try {
          // For now, just mark as completed since the transaction is confirmed
          // In a real implementation, you might want to send the transaction hash to the backend
          console.log('Transfer transaction confirmed:', transferTxHash);

          setState(prev => ({
            ...prev,
            transferTxHash: transferTxHash,
            currentStep: 'store-registration',
            isLoading: false,
            transferCompleted: true
          }));

          showToast({
            type: 'success',
            title: 'ENS Transfer Complete',
            description: 'ENS ownership transferred to registrar contract'
          });

          // Automatically proceed to store step
          setTimeout(() => handleStoreENS(), 1000);
        } catch (error) {
          console.error('Failed to send signed transfer data to backend:', error);
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Backend processing failed'
          }));
          showToast({
            type: 'error',
            title: 'Backend Processing Failed',
            description: error instanceof Error ? error.message : 'Unknown error occurred'
          });
        }
      };

      sendSignedTransferToBackend();
    }
  }, [isTransferConfirmed, transferTxHash, state.ensName, state.chain, state.selectedApplication, state.createdContractAddress, address, token, showToast, handleStoreENS]);

  // Handle transaction errors
  useEffect(() => {
    if (registrarTxError) {
      console.error('=== FRONTEND: Registrar transaction error ===', registrarTxError);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Registrar transaction failed'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Failed',
        description: registrarTxError.message || 'Failed to sign registrar transaction'
      });
    }
  }, [registrarTxError, showToast]);

  useEffect(() => {
    if (transferTxError) {
      console.error('=== FRONTEND: Transfer transaction error ===', transferTxError);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Transfer transaction failed'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Failed',
        description: transferTxError.message || 'Failed to sign transfer transaction'
      });
    }
  }, [transferTxError, showToast]);

  // Handle confirmation errors
  useEffect(() => {
    if (registrarConfirmError) {
      console.error('=== FRONTEND: Registrar confirmation error ===', registrarConfirmError);

      // Extract more detailed error information
      let errorMessage = 'Registrar transaction failed to confirm on the blockchain';
      let errorTitle = 'Transaction Confirmation Failed';

      if (registrarConfirmError.message) {
        const message = registrarConfirmError.message.toLowerCase();
        if (message.includes('execution reverted')) {
          errorTitle = 'Smart Contract Error';
          errorMessage = `The smart contract rejected your transaction. This usually means:\n• You don't own the ENS name "${state.ensName}"\n• The ENS name is not properly registered on ${state.chain}\n• Insufficient permissions or gas\n\nPlease verify ownership and try again.`;
        } else if (message.includes('timeout') || message.includes('timed out')) {
          errorTitle = 'Transaction Timeout';
          errorMessage = 'The transaction is taking longer than expected. It may still complete. Please check your wallet or try again.';
        } else if (message.includes('network')) {
          errorTitle = 'Network Error';
          errorMessage = `Network connection issue on ${state.chain}. Please check your connection and try again.`;
        }
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      showToast({
        type: 'error',
        title: errorTitle,
        description: errorMessage
      });
    }
  }, [registrarConfirmError, showToast, state.ensName, state.chain]);

  useEffect(() => {
    if (transferConfirmError) {
      console.error('=== FRONTEND: Transfer confirmation error ===', transferConfirmError);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Transfer transaction confirmation failed'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Confirmation Failed',
        description: 'Transfer transaction failed to confirm on the blockchain'
      });
    }
  }, [transferConfirmError, showToast]);





  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setState({
        currentStep: isConnected ? 'select-app' : 'connect',
        ensName: '',
        selectedApplication: null,
        chain: 'sepolia',
        isLoading: false,
        registrarCompleted: false,
        transferCompleted: false,
        storeCompleted: false
      });
    }
  }, [isOpen, isConnected]);

  // Auto-advance from connect step when wallet is connected
  useEffect(() => {
    if (isConnected && state.currentStep === 'connect') {
      setState(prev => ({ ...prev, currentStep: 'select-app' }));
    }
  }, [isConnected, state.currentStep]);

  // Handle modal close with confirmation if mid-process
  const handleCloseModal = useCallback(() => {
    if (state.isLoading || state.registrarCompleted || state.transferCompleted) {
      const confirmClose = window.confirm(
        'You are in the middle of the ENS registration process. Are you sure you want to close? Your progress will be lost.'
      );
      if (!confirmClose) return;
    }
    onClose();
  }, [state.isLoading, state.registrarCompleted, state.transferCompleted, onClose]);

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        // Inline the close logic to avoid dependency issues
        if (state.isLoading || state.registrarCompleted || state.transferCompleted) {
          const confirmClose = window.confirm(
            'You are in the middle of the ENS registration process. Are you sure you want to close? Your progress will be lost.'
          );
          if (!confirmClose) return;
        }
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, state.isLoading, state.registrarCompleted, state.transferCompleted, onClose]);



  const handleTransferENS = async () => {
    if (!token || !state.ensName || !state.createdContractAddress || !isConnected || !state.selectedApplication) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      console.log('=== FRONTEND: Preparing local transfer transaction for signing ===');

      // Show preparation status
      showToast({
        type: 'info',
        title: 'Preparing Transfer',
        description: 'Preparing ENS transfer transaction...'
      });

      // Step 1: Validate chain and wallet connection
      const expectedChainId = state.chain === 'sepolia' ? 11155111 : state.chain === 'mainnet' ? 1 : 5;
      if (chainId !== expectedChainId) {
        throw new Error(`Please switch to ${state.chain} network in your wallet`);
      }

      // Validate that we have a contract address from the registrar creation
      if (!state.createdContractAddress) {
        throw new Error('No contract address available. Please complete registrar creation first.');
      }

      // Step 2: Prepare transaction data using direct contract call
      console.log('=== DIRECT CONTRACT CALL: NameWrapper Transfer (Modal) ===');
      console.log('ENS Name:', state.ensName);
      console.log('From:', address);
      console.log('To:', state.createdContractAddress);
      console.log('Chain:', state.chain);

      const { getENSTokenId, validateNameWrapperTransfer } = await import('@/lib/ens-utils');
      const tokenId = getENSTokenId(state.ensName);

      // Import NameWrapper contract address
      const { getNameWrapperAddress } = await import('@/lib/contracts/name-wrapper');
      const nameWrapperAddress = getNameWrapperAddress(state.chain);

      console.log('NameWrapper Contract Address:', nameWrapperAddress);
      console.log('Token ID:', tokenId);

      // Validate the transfer before attempting
      console.log('Validating NameWrapper transfer...');

      // Basic validation - check if user owns the ENS name
      try {
        const validation = await validateNameWrapperTransfer(
          state.ensName,
          address!,
          state.createdContractAddress,
          state.chain
        );

        if (!validation.isValid) {
          throw new Error(validation.error || 'NameWrapper transfer validation failed');
        }

        console.log('NameWrapper transfer validation passed');
      } catch (validationError) {
        console.warn('NameWrapper validation failed, but proceeding with transfer:', validationError);
        // Continue with transfer even if validation fails - let the contract handle it
      }



      // Import NameWrapper contract ABI
      const { NAMEWRAPPER_CONTRACT_ABI } = await import('@/lib/contracts/name-wrapper');

      // Show preparation message
      showToast({
        type: 'info',
        title: 'Transferring ENS',
        description: 'Please confirm the transaction in your wallet'
      });

      // Step 3: Direct contract call using wagmi with proper gas estimation
      try {
        console.log('Calling writeNameWrapperContract with:', {
          address: nameWrapperAddress,
          functionName: 'safeTransferFrom',
          from: address,
          to: state.createdContractAddress,
          tokenId: tokenId,
          amount: 1
        });

        writeNameWrapperContract({
          address: nameWrapperAddress as `0x${string}`,
          abi: NAMEWRAPPER_CONTRACT_ABI,
          functionName: 'safeTransferFrom',
          args: [
            address as `0x${string}`,
            state.createdContractAddress as `0x${string}`,
            BigInt(tokenId),
            BigInt(1),
            '0x' as `0x${string}`
          ],
          gas: BigInt(200000), // Set explicit gas limit for transfer
          chain: undefined,
          account: undefined,
        } as any);
      } catch (contractError) {
        console.error('NameWrapper contract call failed:', contractError);
        throw new Error(`NameWrapper transfer failed: ${contractError instanceof Error ? contractError.message : 'Unknown error'}`);
      }

      // Note: After signing, the useEffect will send signed data to backend

    } catch (error) {
      console.error('Transfer ENS failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to transfer ENS'
      }));
      showToast({
        type: 'error',
        title: 'Transfer Preparation Failed',
        description: error instanceof Error ? error.message : 'Failed to prepare transfer transaction'
      });
    }
  };

  const handleCreateRegistrar = async () => {
    if (!token || !state.ensName || !isConnected || !state.selectedApplication) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      console.log('=== FRONTEND: Preparing local transaction for signing ===');

      // Show preparation status
      showToast({
        type: 'info',
        title: 'Preparing Transaction',
        description: 'Preparing registrar creation transaction...'
      });

      // Step 1: Validate chain and wallet connection
      const { validateNetworkForENS } = await import('@/lib/ens-utils');
      const networkValidation = validateNetworkForENS(state.chain, chainId);
      if (!networkValidation.isValid) {
        throw new Error(networkValidation.error || 'Network validation failed');
      }

      // Step 2: Prepare transaction data using direct contract call
      console.log('=== DIRECT CONTRACT CALL: Create Registrar (Modal) ===');
      console.log('ENS Name:', state.ensName);
      console.log('Chain:', state.chain);

      // Import factory contract address and utility functions
      const { FACTORY_CONTRACT_ADDRESS } = await import('@/lib/contracts/factory-contract');
      const { namehash, validateENSNameDetailed, validateContractExists } = await import('@/lib/ens-utils');

      // Validate ENS name format with detailed error reporting
      const validation = validateENSNameDetailed(state.ensName);
      if (!validation.isValid) {
        throw new Error(validation.error || 'Invalid ENS name format');
      }

      const parentNode = namehash(state.ensName.toLowerCase());

      console.log('Factory Contract Address:', FACTORY_CONTRACT_ADDRESS);
      console.log('Parent Node Hash:', parentNode);
      console.log('ENS Name Validation:', {
        name: state.ensName,
        isValid: validation.isValid,
        parentNode: parentNode
      });

      // Validate that the factory contract exists on the current network
      try {
        const { ethers } = await import('ethers');
        const rpcUrl = state.chain === 'sepolia'
          ? 'https://sepolia.drpc.org'
          : 'https://eth.llamarpc.com';
        const provider = new ethers.JsonRpcProvider(rpcUrl);

        const contractExists = await validateContractExists(FACTORY_CONTRACT_ADDRESS, provider);
        if (!contractExists) {
          throw new Error(`Factory contract not found at ${FACTORY_CONTRACT_ADDRESS} on ${state.chain} network. Please check the contract address and network.`);
        }
        console.log('Factory contract validation passed');
      } catch (contractError) {
        console.error('Contract validation failed:', contractError);
        throw new Error(`Contract validation failed: ${contractError instanceof Error ? contractError.message : 'Unknown error'}`);
      }

      // Validate ENS ownership before attempting transaction
      try {
        const { checkENSOwnership } = await import('@/lib/ens-utils');
        console.log('Checking ENS ownership...');

        showToast({
          type: 'info',
          title: 'Validating Ownership',
          description: 'Checking if you own the ENS name...'
        });

        const ownershipCheck = await checkENSOwnership(state.ensName, address!, state.chain);

        if (!ownershipCheck.isOwner) {
          throw new Error(ownershipCheck.error || 'ENS ownership validation failed');
        }

        console.log('ENS ownership validation passed');
      } catch (ownershipError) {
        console.error('ENS ownership validation failed:', ownershipError);
        throw new Error(`ENS ownership validation failed: ${ownershipError instanceof Error ? ownershipError.message : 'Unknown error'}`);
      }



      // Import factory contract ABI
      const { FACTORY_CONTRACT_ABI } = await import('@/lib/contracts/factory-contract');

      // Show preparation message
      showToast({
        type: 'info',
        title: 'Creating Registrar',
        description: 'Please confirm the transaction in your wallet'
      });

      // Step 3: Direct contract call using wagmi with improved gas estimation
      try {
        console.log('Calling writeFactoryContract with:', {
          address: FACTORY_CONTRACT_ADDRESS,
          functionName: 'createSubnameRegistrar',
          args: [parentNode],
          parentNodeType: typeof parentNode,
          parentNodeLength: parentNode.length,
          ensName: state.ensName,
          chain: state.chain,
          chainId: chainId
        });

        // Validate parentNode format
        if (!parentNode || !parentNode.startsWith('0x') || parentNode.length !== 66) {
          throw new Error(`Invalid parent node hash: ${parentNode}. Expected 32-byte hex string.`);
        }

        writeFactoryContract({
          address: FACTORY_CONTRACT_ADDRESS as `0x${string}`,
          abi: FACTORY_CONTRACT_ABI,
          functionName: 'createSubnameRegistrar',
          args: [parentNode as `0x${string}`],
          gas: BigInt(800000), // Increased gas limit for better reliability
          chain: undefined,
          account: undefined,
        } as any);
      } catch (contractError) {
        console.error('Contract call failed:', contractError);
        const errorMessage = contractError instanceof Error ? contractError.message : 'Unknown error';

        // Provide more specific error messages based on common failure scenarios
        if (errorMessage.includes('execution reverted')) {
          throw new Error(`Transaction failed: The smart contract rejected the transaction. This could be because:\n• You don't own the ENS name "${state.ensName}"\n• The ENS name is not properly registered\n• The contract is not authorized to create registrars\n• Network congestion or gas issues\n\nPlease verify you own the ENS name and try again.`);
        } else if (errorMessage.includes('insufficient funds')) {
          throw new Error('Insufficient funds for gas fees. Please add more ETH to your wallet and try again.');
        } else if (errorMessage.includes('user rejected')) {
          throw new Error('Transaction was rejected in your wallet. Please try again and confirm the transaction.');
        } else {
          throw new Error(`Contract call failed: ${errorMessage}`);
        }
      }

      // Note: After signing, the useEffect will send signed data to backend

    } catch (error) {
      console.error('Create registrar failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create registrar'
      }));
      showToast({
        type: 'error',
        title: 'Transaction Preparation Failed',
        description: error instanceof Error ? error.message : 'Failed to prepare transaction'
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleCloseModal}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="sticky top-0 bg-white border-b border-[#B497D6]/20 rounded-t-2xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
              Register ENS Domain
            </h2>
            <Button
              onClick={handleCloseModal}
              variant="outline"
              size="sm"
              className="border-gray-300 text-gray-600 hover:bg-gray-100"
            >
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Progress Indicator */}
          <div className="grid grid-cols-5 gap-2">
            {[
              { step: 'connect', label: 'Connect', stepNumber: 1 },
              { step: 'select-app', label: 'Select App', stepNumber: 2 },
              { step: 'enter-ens', label: 'ENS & Registrar', stepNumber: 3 },
              { step: 'transfer-ens', label: 'Transfer', stepNumber: 4 },
              { step: 'store-registration', label: 'Complete', stepNumber: 5 }
            ].map(({ step, label, stepNumber }) => {
              const stepOrder: ModalStep[] = ['connect', 'select-app', 'enter-ens', 'transfer-ens', 'store-registration'];
              const currentIndex = stepOrder.indexOf(state.currentStep);
              const stepIndex = stepOrder.indexOf(step as ModalStep);
              const isCompleted = stepIndex < currentIndex;
              const isActive = stepIndex === currentIndex;

              return (
                <div key={step} className="flex flex-col items-center text-center">
                  <div className={`
                    flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all mb-1
                    ${isCompleted ? 'bg-green-500 border-green-500 text-white' : ''}
                    ${isActive ? 'bg-[#4A148C] border-[#4A148C] text-white' : ''}
                    ${!isActive && !isCompleted ? 'bg-gray-100 border-gray-300 text-gray-400' : ''}
                  `}>
                    {isCompleted ? (
                      <CheckCircleIcon className="h-4 w-4" />
                    ) : (
                      <span className="text-xs font-bold">{stepNumber}</span>
                    )}
                  </div>
                  <p className={`text-xs font-medium ${
                    isCompleted ? 'text-green-600' :
                    isActive ? 'text-[#4A148C]' : 'text-gray-400'
                  }`}>
                    {label}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Modal Content */}
        <div className="p-6">
          {/* Step 1: Connect Wallet */}
          {state.currentStep === 'connect' && (
            <div className="space-y-6">
              <div className="text-center">
                <WalletIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Connect Your Wallet</h3>
                <p className="text-gray-600 mb-6">
                  Connect your wallet to verify ENS ownership and interact with the blockchain
                </p>
              </div>

              <div className="flex justify-center">
                <ConnectButton />
              </div>

              {isConnected && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-center">
                  <CheckCircleIcon className="h-6 w-6 text-green-600 mx-auto mb-2" />
                  <p className="text-green-700 font-medium">Wallet Connected Successfully!</p>
                  <p className="text-green-600 text-sm">Proceeding to application selection...</p>
                </div>
              )}
            </div>
          )}

          {/* Step 2: Select Application */}
          {state.currentStep === 'select-app' && (
            <div className="space-y-6">
              <div className="text-center">
                <GlobeIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Select Application</h3>
                <p className="text-gray-600 mb-6">
                  Choose which application you want to register an ENS domain for
                </p>
              </div>

              <ApplicationSelection
                onApplicationSelect={(_, application) => {
                  setState(prev => ({
                    ...prev,
                    selectedApplication: application,
                    currentStep: 'enter-ens'
                  }));
                }}
                selectedApplicationId={state.selectedApplication?.appId ? String(state.selectedApplication.appId) : undefined}
              />
            </div>
          )}

          {/* Step 3: Enter ENS & Create Registrar */}
          {state.currentStep === 'enter-ens' && (
            <div className="space-y-6">
              <div className="text-center">
                <SettingsIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Enter ENS Name & Create Registrar</h3>
                <p className="text-gray-600 mb-6">
                  Enter your ENS name and create a subname registrar contract
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="ensName" className="text-sm font-medium text-gray-700">
                    ENS Name *
                  </Label>
                  <Input
                    id="ensName"
                    placeholder="myproject.eth"
                    value={state.ensName}
                    onChange={(e) => setState(prev => ({ ...prev, ensName: e.target.value }))}
                    className="mt-1 border-[#B497D6]/30 focus:border-[#4A148C] focus:ring-[#4A148C]"
                  />
                  <div className="mt-2 space-y-1">
                    <p className="text-xs text-gray-500">
                      Enter the ENS name you own (e.g., myproject.eth, mydao.com, example.xyz)
                    </p>
                    <p className="text-xs text-amber-600">
                      ⚠️ You must own this ENS name with the connected wallet
                    </p>
                    {state.ensName && (() => {
                      const { validateENSNameDetailed } = require('@/lib/ens-utils');
                      const validation = validateENSNameDetailed(state.ensName);
                      if (!validation.isValid && validation.error) {
                        return (
                          <p className="text-xs text-red-600 bg-red-50 p-2 rounded border border-red-200">
                            ❌ {validation.error}
                          </p>
                        );
                      }
                      return null;
                    })()}
                  </div>
                </div>

                <div>
                  <Label htmlFor="walletAddress" className="text-sm font-medium text-gray-700">
                    Connected Wallet
                  </Label>
                  <div className="mt-1 p-3 bg-gradient-to-r from-[#4A148C]/5 to-[#7B1FA2]/5 border border-[#B497D6]/30 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <code className="text-sm font-mono text-[#4A148C]">
                        {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}
                      </code>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      This wallet must own the ENS name you want to register
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="chain" className="text-sm font-medium text-gray-700">
                    Blockchain Network *
                  </Label>
                  <select
                    id="chain"
                    value={state.chain}
                    onChange={(e) => setState(prev => ({ ...prev, chain: e.target.value }))}
                    className="mt-1 w-full px-3 py-2 border border-[#B497D6]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#4A148C] focus:border-[#4A148C]"
                  >
                    <option value="sepolia">Sepolia Testnet</option>
                    <option value="mainnet">Ethereum Mainnet</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Select the network where your ENS name is registered
                  </p>
                </div>
              </div>

              {/* Validation Summary */}
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Before Creating Registrar:</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li className="flex items-center gap-2">
                    {state.ensName ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-blue-400" />
                    )}
                    ENS name entered
                  </li>
                  <li className="flex items-center gap-2">
                    {isConnected ? (
                      <CheckCircleIcon className="h-3 w-3 text-green-600" />
                    ) : (
                      <div className="h-3 w-3 rounded-full border border-blue-400" />
                    )}
                    Wallet connected
                  </li>
                  <li className="flex items-center gap-2">
                    {(() => {
                      if (!state.ensName) return <div className="h-3 w-3 rounded-full border border-blue-400" />;
                      const { validateENSNameDetailed } = require('@/lib/ens-utils');
                      const validation = validateENSNameDetailed(state.ensName);
                      return validation.isValid ? (
                        <CheckCircleIcon className="h-3 w-3 text-green-600" />
                      ) : (
                        <div className="h-3 w-3 rounded-full border border-blue-400" />
                      );
                    })()}
                    Valid ENS format
                  </li>
                </ul>
              </div>

              {/* Debug Information */}
              {process.env.NODE_ENV === 'development' && (
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-xs">
                  <h5 className="font-medium text-gray-700 mb-1">Debug Info:</h5>
                  <div className="text-gray-600 space-y-1">
                    <div>ENS Name: {state.ensName || 'Not set'}</div>
                    <div>Chain: {state.chain} (ID: {chainId})</div>
                    <div>From Address: {address ? String(address) : 'Not connected'}</div>
                    <div>App ID: {String(state.selectedApplication?.appId || 'Not set')}</div>
                    <div>Token Available: {token ? 'Yes' : 'No'}</div>
                    <div>Contract Address: {state.createdContractAddress || 'Not set'}</div>
                    {state.ensName && (() => {
                      try {
                        const { namehash, validateENSNameDetailed } = require('@/lib/ens-utils');
                        const validation = validateENSNameDetailed(state.ensName);
                        const parentNode = namehash(state.ensName.toLowerCase());
                        return (
                          <>
                            <div>ENS Validation: {validation.isValid ? '✅ Valid' : `❌ ${validation.error}`}</div>
                            <div>Parent Node: {parentNode}</div>
                            <div>Factory Contract: 0x0dBA7bd3240c86090Cd53bE0D9DaB99b466A36D3</div>
                            <div>Registrar Status: {state.registrarCompleted ? '✅ Completed' : '⏳ Pending'}</div>
                            <div>Transfer Status: {state.transferCompleted ? '✅ Completed' : '⏳ Pending'}</div>
                            <div>Registrar TX: {registrarTxHash ? `${registrarTxHash.slice(0, 10)}...` : 'None'}</div>
                            <div>Transfer TX: {transferTxHash ? `${transferTxHash.slice(0, 10)}...` : 'None'}</div>
                          </>
                        );
                      } catch (e) {
                        return <div>Debug Error: {String(e)}</div>;
                      }
                    })()}
                  </div>

                  {/* Test API Button */}
                  <Button
                    onClick={async () => {
                      if (!token || !state.selectedApplication || !state.ensName || !address) {
                        console.log('Missing required data for API test');
                        return;
                      }

                      console.log('=== TESTING API CALL ===');
                      try {
                        const response = await apiService.prepareRegistrarTransaction(
                          {
                            ensName: state.ensName,
                            chain: state.chain
                          },
                          token,
                          state.selectedApplication.appId
                        );
                        console.log('Test API Response:', JSON.stringify(response, null, 2));
                      } catch (error) {
                        console.error('Test API Error:', error);
                      }
                    }}
                    className="mt-2 text-xs px-2 py-1 bg-blue-500 text-white rounded"
                    disabled={!token || !state.selectedApplication || !state.ensName || !address}
                  >
                    Test API Call
                  </Button>
                </div>
              )}

              {/* Wallet Signing Instructions */}
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <WalletIcon className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">Wallet Signature Required</p>
                    <p className="text-xs mt-1">
                      You'll need to sign this transaction in your connected wallet to create the registrar contract.
                      Make sure you have enough ETH for gas fees.
                    </p>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleCreateRegistrar}
                disabled={(() => {
                  if (!state.ensName || !isConnected || state.isLoading || state.registrarCompleted || isRegistrarPending || isRegistrarConfirming) {
                    return true;
                  }
                  const { validateENSNameDetailed } = require('@/lib/ens-utils');
                  const validation = validateENSNameDetailed(state.ensName);
                  return !validation.isValid;
                })()}
                className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {state.registrarCompleted ? (
                  <>
                    <CheckCircleIcon className="h-4 w-4 mr-2 text-green-500" />
                    Registrar Created Successfully
                  </>
                ) : isRegistrarConfirming ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Processing Transaction...
                  </>
                ) : isRegistrarPending ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Waiting for Signature...
                  </>
                ) : state.isLoading ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Preparing Transaction...
                  </>
                ) : !state.ensName ? (
                  'Enter ENS Name to Continue'
                ) : !isConnected ? (
                  'Connect Wallet First'
                ) : (() => {
                  const { validateENSNameDetailed } = require('@/lib/ens-utils');
                  const validation = validateENSNameDetailed(state.ensName);
                  return !validation.isValid ? (validation.error || 'Invalid ENS Name Format') : 'Create Subname Registrar Contract';
                })()}
              </Button>

              {state.registrarCompleted && state.createdContractAddress && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900 mb-2">✅ Registrar Created Successfully!</h4>
                  <div className="text-xs text-green-700 space-y-1">
                    <p><strong>Contract Address:</strong></p>
                    <code className="block bg-green-100 p-2 rounded text-xs break-all">
                      {state.createdContractAddress}
                    </code>
                    <p className="mt-2">You can now proceed to transfer your ENS name to this contract.</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 4: Transfer ENS to Contract */}
          {state.currentStep === 'transfer-ens' && (
            <div className="space-y-6">
              <div className="text-center">
                <ArrowUpDownIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Transfer ENS to Contract</h3>
                <p className="text-gray-600 mb-6">
                  Transfer ownership of your ENS name to the registrar contract
                </p>
              </div>

              <div className="space-y-4">
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                  <p className="text-sm font-medium text-blue-900 mb-1">ENS Name to Transfer</p>
                  <p className="text-xl font-bold text-blue-700">{state.ensName || 'Not specified'}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <p className="text-sm font-medium text-gray-700 mb-2">From (Current Owner)</p>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <code className="text-sm font-mono text-gray-900">
                        {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}
                      </code>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Your wallet address</p>
                  </div>

                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm font-medium text-green-700 mb-2">To (New Owner)</p>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${state.createdContractAddress ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      <code className="text-sm font-mono text-green-900">
                        {state.createdContractAddress
                          ? `${state.createdContractAddress.slice(0, 6)}...${state.createdContractAddress.slice(-4)}`
                          : 'Waiting for contract...'
                        }
                      </code>
                    </div>
                    <p className="text-xs text-green-600 mt-1">Registrar contract address</p>
                  </div>
                </div>
              </div>

              {/* Wallet Signing Instructions for Transfer */}
              {state.registrarCompleted && !state.transferCompleted && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start gap-2">
                    <ArrowUpDownIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">Transfer ENS Ownership</p>
                      <p className="text-xs mt-1">
                        You'll need to sign a transaction to transfer your ENS name ownership to the registrar contract.
                        This enables subname claiming functionality.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <Button
                onClick={handleTransferENS}
                disabled={state.isLoading || !state.createdContractAddress || !state.registrarCompleted || state.transferCompleted || isTransferPending || isTransferConfirming}
                className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA] disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {state.transferCompleted ? (
                  <>
                    <CheckCircleIcon className="h-4 w-4 mr-2 text-green-500" />
                    ENS Transfer Completed Successfully
                  </>
                ) : isTransferConfirming ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Processing Transaction...
                  </>
                ) : isTransferPending ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Waiting for Signature...
                  </>
                ) : state.isLoading ? (
                  <>
                    <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                    Preparing Transfer...
                  </>
                ) : !state.registrarCompleted ? (
                  'Complete Step 3: Create Registrar First'
                ) : !state.createdContractAddress ? (
                  'Waiting for Contract Address...'
                ) : (
                  'Transfer ENS Name to Contract'
                )}
              </Button>

              {state.transferCompleted && state.transferTxHash && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900 mb-2">✅ Transfer Completed Successfully!</h4>
                  <div className="text-xs text-green-700 space-y-1">
                    <p><strong>Transaction Hash:</strong></p>
                    <code className="block bg-green-100 p-2 rounded text-xs break-all">
                      {state.transferTxHash}
                    </code>
                    <p className="mt-2">Your ENS name has been transferred to the registrar contract. Proceeding to store registration...</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 5: Store Registration */}
          {state.currentStep === 'store-registration' && (
            <div className="space-y-6">
              <div className="text-center">
                <CheckCircleIcon className="h-16 w-16 mx-auto text-[#4A148C] mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Store Registration</h3>
                <p className="text-gray-600 mb-6">
                  Save your ENS registration details in the Crefy Connect backend
                </p>
              </div>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">📝 Registration Summary</h4>
                <div className="text-sm text-blue-700 space-y-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium">ENS Name:</p>
                      <p className="font-mono text-xs bg-blue-100 p-1 rounded">{state.ensName}</p>
                    </div>
                    <div>
                      <p className="font-medium">Application:</p>
                      <p className="text-xs">{state.selectedApplication?.name}</p>
                    </div>
                    <div>
                      <p className="font-medium">Contract Address:</p>
                      <p className="font-mono text-xs bg-blue-100 p-1 rounded break-all">
                        {state.createdContractAddress || 'Not available'}
                      </p>
                    </div>
                    <div>
                      <p className="font-medium">Network:</p>
                      <p className="text-xs capitalize">{state.chain}</p>
                    </div>
                  </div>
                </div>
              </div>

              {state.isLoading ? (
                <div className="flex flex-col items-center justify-center py-8 space-y-4">
                  <LoaderIcon className="h-8 w-8 animate-spin text-[#4A148C]" />
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">Storing Registration...</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Saving your ENS integration details to Crefy Connect backend
                    </p>
                  </div>
                </div>
              ) : state.storeCompleted ? (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900 mb-2">✅ Registration Stored Successfully!</h4>
                  <p className="text-sm text-green-700">
                    Your ENS integration has been completed and saved. Redirecting to dashboard...
                  </p>
                </div>
              ) : (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                  <h4 className="text-sm font-medium text-amber-900 mb-2">⏳ Ready to Store Registration</h4>
                  <p className="text-sm text-amber-700">
                    All previous steps completed successfully. The registration will be stored automatically.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Modal Navigation */}
          <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <Button
              onClick={() => {
                const stepOrder: ModalStep[] = ['connect', 'select-app', 'enter-ens', 'transfer-ens', 'store-registration'];
                const currentIndex = stepOrder.indexOf(state.currentStep);
                if (currentIndex > 0) {
                  setState(prev => ({ ...prev, currentStep: stepOrder[currentIndex - 1] }));
                }
              }}
              variant="outline"
              disabled={state.currentStep === 'connect' || state.isLoading}
              className="border-[#B497D6]/30 text-[#4A148C] hover:bg-[#4A148C]/5"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Previous
            </Button>

            <div className="text-sm text-gray-500">
              Step {['connect', 'select-app', 'enter-ens', 'transfer-ens', 'store-registration'].indexOf(state.currentStep) + 1} of 5
            </div>

            <Button
              onClick={() => {
                const stepOrder: ModalStep[] = ['connect', 'select-app', 'enter-ens', 'transfer-ens', 'store-registration'];
                const currentIndex = stepOrder.indexOf(state.currentStep);
                if (currentIndex < stepOrder.length - 1) {
                  setState(prev => ({ ...prev, currentStep: stepOrder[currentIndex + 1] }));
                }
              }}
              variant="outline"
              disabled={
                state.currentStep === 'store-registration' ||
                state.isLoading ||
                (state.currentStep === 'connect' && !isConnected) ||
                (state.currentStep === 'select-app' && !state.selectedApplication) ||
                (state.currentStep === 'enter-ens' && !state.registrarCompleted) ||
                (state.currentStep === 'transfer-ens' && !state.transferCompleted)
              }
              className="border-[#B497D6]/30 text-[#4A148C] hover:bg-[#4A148C]/5"
            >
              Next
              <ArrowRightIcon className="h-4 w-4 ml-2" />
            </Button>
          </div>

          {/* Debug Button */}
          <div className="mt-4 text-center">
            <button
              onClick={() => setShowDebugInfo(true)}
              className="text-xs text-gray-500 hover:text-gray-700 underline"
            >
              Debug Info
            </button>
          </div>
        </div>
      </div>

      {/* Debug Modal */}
      {showDebugInfo && (
        <ENSDebugInfo
          ensName={state.ensName}
          onClose={() => setShowDebugInfo(false)}
        />
      )}
    </div>
  );
}
