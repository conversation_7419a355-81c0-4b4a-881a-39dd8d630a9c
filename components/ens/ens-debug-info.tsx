import React from 'react';
import { useAccount, useChainId } from 'wagmi';

interface ENSDebugInfoProps {
  ensName?: string;
  onClose: () => void;
}

export const ENSDebugInfo: React.FC<ENSDebugInfoProps> = ({ ensName, onClose }) => {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();

  const getChainName = (id: number) => {
    switch (id) {
      case 1: return 'Mainnet';
      case ********: return 'Sepolia';
      case 5: return 'Goerli';
      case 17000: return 'Holesky';
      default: return `Unknown (${id})`;
    }
  };

  const getContractAddresses = () => {
    if (chainId === ********) { // Sepolia
      return {
        factory: '0x0dBA7bd3240c86090Cd53bE0D9DaB99b466A36D3',
        nameWrapper: '0x0635513f179D50A207757E05759CbD106d7dFcE8'
      };
    }
    return {
      factory: 'Not configured',
      nameWrapper: 'Not configured'
    };
  };

  const contracts = getContractAddresses();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">ENS Debug Information</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        
        <div className="space-y-3 text-sm">
          <div>
            <strong>Wallet Status:</strong>
            <div className="ml-2">
              <div>Connected: {isConnected ? '✅ Yes' : '❌ No'}</div>
              <div>Address: {address || 'Not connected'}</div>
            </div>
          </div>

          <div>
            <strong>Network:</strong>
            <div className="ml-2">
              <div>Chain ID: {chainId}</div>
              <div>Network: {getChainName(chainId)}</div>
              <div>Supported: {chainId === ******** ? '✅ Yes' : '❌ No (Switch to Sepolia)'}</div>
            </div>
          </div>

          <div>
            <strong>ENS Name:</strong>
            <div className="ml-2">
              <div>Name: {ensName || 'Not provided'}</div>
              <div>Valid format: {ensName?.endsWith('.eth') ? '✅ Yes' : '❌ No'}</div>
            </div>
          </div>

          <div>
            <strong>Contract Addresses:</strong>
            <div className="ml-2">
              <div>Factory: {contracts.factory}</div>
              <div>NameWrapper: {contracts.nameWrapper}</div>
            </div>
          </div>

          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <strong>Common Issues:</strong>
            <ul className="mt-2 text-xs space-y-1">
              <li>• Make sure you're on Sepolia network</li>
              <li>• Ensure ENS name ends with .eth</li>
              <li>• Check that you own the ENS name</li>
              <li>• Verify the ENS name is wrapped</li>
              <li>• Ensure sufficient ETH for gas fees</li>
            </ul>
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};
